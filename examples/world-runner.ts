import { fetch_playthroughs, type Playthrough } from "@/db/playthrough";
import { fetch_worlds, type World } from "@/db/world";
import { create_mastra_instance } from "@/mastra";
import { GameLoop } from "@/mastra/workflows/game-flow";

const mastra = create_mastra_instance();

async function gimme_world_and_playthrough(): Promise<[World, Playthrough]> {
	const worlds = await fetch_worlds();
	const playthroughs = await fetch_playthroughs();

	if (worlds.length > 0 && playthroughs.length > 0) return [worlds[0], playthroughs[0]];

	const workflow = mastra.getWorkflow("create_world_workflow");
	const run = await workflow.createRun();
	const outcome = await run.start({ inputData: { prompt: "Please generate me a high fantasy world with a rich lore and a lot of details for my text rpg game" } });

	if (outcome.status === "success") return (outcome as unknown as { result: [World, Playthrough] }).result;

	throw Error("Failed to create world");
}

const [world, playthrough] = await gimme_world_and_playthrough();

await GameLoop(world, playthrough);
