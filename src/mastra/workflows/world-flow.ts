import path from "node:path";
import { RuntimeContext } from "@mastra/core/runtime-context";
import { createStep, createWorkflow } from "@mastra/core/workflows";
import { z } from "zod";
import { save_world, world_schema } from "@/db/world";
import { clean_name, generate, parseZodObjectFromText } from "@/utils/helpers";

import {
	create_map_graph_and_biomes,
	createWorldBiomesAndLocationsReturnSchema,
	createWorldBiomesAndLocationsSchema,
	map_graph_result_schema,
	PlaceSchema,
	type WorldConfig,
	worldConfigSchema,
} from "../../orchestrator";

import type { WorldAgentRuntimeContext } from "../agents/world";
import { save_playthrough } from "@/db/playthrough";

const create_world_step = createStep({
	id: "create-world-step",
	description: "generate the world",
	inputSchema: z.object({
		prompt: z.string().describe("The prompt to generate the world").default("Please generate me a high fantasy world with a rich lore and a lot of details for my text rpg game"),
	}),
	outputSchema: worldConfigSchema,
	execute: async ({ inputData, mastra }) => {
		console.log("started create-world-step");
		// hard coded for now
		// todo: create world via ai
		// create world map via ai
		// create biomes in json format
		// create the neighbors of each location
		// get starting location
		// run map creation function
		// create an image for every region (as we unlock the regions)
		// for every region we need to ask what are the places/locations inside
		// put player at starting location

		return (await Bun.file("assets/worlds/0.json").json()) as WorldConfig;
		// const world = await mastra.getAgent("world-agent").generate(inputData.prompt);
		// return createWorldReturnSchema.parse(world.text);
	},
});

const create_world_map_step = createStep({
	id: "create-world-map-step",
	description: "create the world map",
	inputSchema: worldConfigSchema,
	outputSchema: z.object({
		world_map_image_url_or_base64: z.string(),
	}),
	execute: async ({ inputData, mastra }) => {
		console.log("create the world map");
		const fullpath = path.join(import.meta.dirname, "..", "..", "..", "assets", "maps", "0", "_world.png");
		await new Promise((resolve, reject) => resolve(true));
		return {
			world_map_image_url_or_base64: fullpath,
		};
	},
});

export const create_world_biomes_and_locations_step = createStep({
	id: "create-world-biomes-and-locations-step",
	description: "create the world biomes and locations",
	inputSchema: z.object({
		world_map_image_url_or_base64: z.string(),
	}),
	outputSchema: createWorldBiomesAndLocationsReturnSchema,
	execute: async ({ inputData, mastra }) => {
		console.log("create-world-biomes-and-locations-step");
		const runtimeContext = new RuntimeContext<WorldAgentRuntimeContext>();
		runtimeContext.set("type", "analyze-world-map");

		const world = mastra.getAgent("world-agent");
		const final_location = "crypt_of_the_clockwork_king";

		// only temporary do this, in the future we will use a more robust method
		const image = await Bun.file(inputData.world_map_image_url_or_base64).arrayBuffer();
		const response = await generate(world, runtimeContext, { role: "user", content: [{ type: "image", image: image }] });
		console.log(response.text);
		console.log("------------------------------");
		const world_biomes_and_locations = parseZodObjectFromText(createWorldBiomesAndLocationsSchema, response.text);

		const connections = {};
		const biomes = {};

		for (const [location, neighbors] of Object.entries(world_biomes_and_locations.connections)) {
			connections[clean_name(location)] = neighbors.map((neighbor) => clean_name(neighbor));
		}

		for (const [biome, locations] of Object.entries(world_biomes_and_locations.biomes)) {
			biomes[clean_name(biome)] = Object.fromEntries(Object.entries(locations).map(([location, description]) => [clean_name(location), description]));
		}

		return {
			connections,
			biomes,
			final_location,
		};
	},
});

export const create_world_biomes_and_locations_graph_step = createStep({
	id: "create-world-biomes-and-locations-graph-step",
	description: "create the world biomes and locations graph",
	inputSchema: createWorldBiomesAndLocationsReturnSchema,
	outputSchema: map_graph_result_schema,
	execute: async ({ inputData, getStepResult, mastra }) => {
		console.log("create_world_biomes_and_locations_graph_step");
		const world_data = getStepResult(create_world_step);
		const { connections, biomes, final_location } = inputData;

		const starting_location = Object.keys(connections)[0];
		const starting_location_image = await get_image_from_location(starting_location);

		const crated_graph_map = create_map_graph_and_biomes(connections, biomes, starting_location, final_location);

		const world = mastra.getAgent("world-agent");
		const runtimeContext = new RuntimeContext<WorldAgentRuntimeContext>();
		runtimeContext.set("type", "analyze-world-location");

		const location_response = await generate(world, runtimeContext, {
			role: "user",
			content: [{ type: "image", image: starting_location_image }],
		});

		const location_object = parseZodObjectFromText(z.record(z.string(), PlaceSchema), location_response.text);

		for (const [location, location_data] of Object.entries(location_object)) {
			const location_name = clean_name(location);
			if (location_name === starting_location) continue;


			crated_graph_map.locations[starting_location].biome = 'testy';
			crated_graph_map.locations[starting_location].places[location] = location_data;
		}
		console.log(crated_graph_map.locations[starting_location].places);

		// console.log(location_object);

		const world_db = {
			version: 0,
			config: {
				...world_data,
				biomes: crated_graph_map.biomes,
				locations: crated_graph_map.locations,
				starting_location,
				final_location,
			},
		};

		console.log("saving world");
		await save_world(world_db);
		console.log("saved world");

		const playthrough_db = {
			version: 0,
			player: {
				username: "test",
				inventory: {
					items: [],
					currency: 0,
				},
				location: starting_location,
				unlock_lvl: 0,
				relationships: [],
			},
			world: 0, // todo: get the world id from the db
			creator: "test",
		}

		console.log("saving playthrough");
		await save_playthrough(playthrough_db);
		console.log("saved playthrough");

		return [world_db, playthrough_db];
	},
});

export const create_initial_region_data_step = createStep({
	id: "create_initial_region_data_step",
	description: "Create initial region data",
	inputSchema: world_schema,
	outputSchema: z.void(),
	async execute() {},
});

export async function get_image_from_location(location: string) {
	const arraybuffer = await Bun.file(`assets/maps/0/${location}.png`).arrayBuffer();
	return new Uint8Array(arraybuffer);
}

export const create_world_workflow = createWorkflow({
	id: "create-world-workflow",
	description: "create the world",
	inputSchema: z.object({
		prompt: z.string().describe("The prompt to generate the world").default("Please generate me a high fantasy world with a rich lore and a lot of details for my text rpg game"),
	}),
	outputSchema: z.void(),
})
	.then(create_world_step)
	.then(create_world_map_step)
	.then(create_world_biomes_and_locations_step)
	.then(create_world_biomes_and_locations_graph_step)
	.commit();
