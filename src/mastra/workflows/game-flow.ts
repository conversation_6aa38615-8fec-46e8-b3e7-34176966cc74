import { createWorkflow } from "@mastra/core";
import type { World } from "@/db/world";
import { get_travel_locations } from "@/orchestrator";
import { createInterface } from 'readline/promises';
import { stdin as input, stdout as output } from 'process';
import { update_playthrough, type Playthrough } from "@/db/playthrough";

// Terminal color codes
const colors = {
	reset: "\x1b[0m",
	bold: "\x1b[1m",
	cyan: "\x1b[36m",
	yellow: "\x1b[33m",
	green: "\x1b[32m",
	magenta: "\x1b[35m",
	blue: "\x1b[94m",
	gray: "\x1b[90m",
};
// const gameflow = createWorkflow({
//   id: "gameflow",
//   description: "A game flow for a simple game",
// })
//

export async function LevelUp(world: World, playthrough: Playthrough) {
	console.log(`${colors.bold}${colors.yellow}Unlocked new biome!${colors.reset}`);
	++playthrough.player.unlock_lvl;

	update_playthrough(playthrough);
	console.log("Generating new places...");
	// todo generate new places
	console.log("Updating world...");
	// todo update world db

	//update playthrough every time place changes
}

export async function PlaceLoop(world: World, playthrough: Playthrough, location: string, place: string) {
	const rl = createInterface({ input, output });

	while(true) {
		const place_obj = world.config.locations[location].places[place];

		let question = `${colors.bold}${colors.cyan}You're exploring ${place}. ${place_obj.description}${colors.reset} \n\n(${0}) Leave\n`;
		const answer = await rl.question(
			question,
		);

		if (answer == "0") {
			if (world.config.locations[location].unlock_lvl == playthrough.player.unlock_lvl) {
				LevelUp(world, playthrough);
			}
			return;
		}
	}
}

export async function LocationLoop(world: World, playthrough: Playthrough, location: string) {
	const rl = createInterface({ input, output });

	while(true) {
		const places = world.config.locations[location].places;
		let index = 0;
		let options = "";
		for (const [key, place] of Object.entries(places)) {
  			options += `(${index++}) ${key}\n`;
		}

		let question = `${colors.bold}${colors.cyan}You're in ${location}. Explore: ${colors.reset} \n\n${options}(${index}) Leave\n`;
		const answer = await rl.question(
			question,
		);

		if (answer == index.toString()) return;

		await PlaceLoop(world, playthrough, location, Object.keys(places)[answer]);
	}
}

export async function GameLoop(world: World, playthrough: Playthrough) {
	// Set up stdin to read line by line
	process.stdin.setEncoding("utf-8");

	let current_location = world.config.starting_location;
	
	await LocationLoop(world, playthrough, current_location);

	const rl = createInterface({ input, output });
	
	while(true) {
		const [unlocked_locations, locked_locations] = Array.from(get_travel_locations(current_location, playthrough.player.unlock_lvl, world.config.locations));
		const arr_unlocked_locations = Array.from(unlocked_locations);
		const arr_locked_locations = Array.from(locked_locations);
		const total_locations = arr_unlocked_locations.length + arr_locked_locations.length;

		const question = `${colors.bold}${colors.cyan}You're about to leave ${current_location}. Go to: ${colors.reset} \n${arr_unlocked_locations
				.map((l, index) => ` (${index}) ${l}`)
				.join("\n")}\n${colors.gray}${arr_locked_locations
				.map((l) => ` (x) ${l}\n`)
				.join("")}${colors.reset} (${total_locations}) I've changed my mind\n`;
		const answer = await rl.question(
			question,
		);

		const numericAnswer = parseInt('0' + answer.replace(/\D/g, ''), 10);

		if (numericAnswer < Array.from(unlocked_locations).length) {
			current_location = Array.from(unlocked_locations)[answer];
			playthrough.player.location = current_location;
			update_playthrough(playthrough);
		}
		await LocationLoop(world, playthrough, current_location);
	}
}
